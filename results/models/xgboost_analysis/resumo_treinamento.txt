RESUMO DO TREINAMENTO XGBOOST BINÁRIO - SINAIS DE TRADING
======================================================================

MODELO BINÁRIO:
  • Tipo: XGBoost Binário
  • Classes: 0=Venda, 1=Compra
  • Função de perda: logloss
  • Threshold de probabilidade: 0.5 (sinais só gerados se prob > 0.5)
  • Features básicas: pct_change da média OHLC (variação percentual), Volume, Spread, Volatilidade
  • Features econométricas: Parkinson, MFI, EMV, Am<PERSON>ud, Roll Spread,
    Hurst, Vol/Volume, CMF, A/D Line, Volume Oscillator (11 features)
  • Features lagged: 5 lags para cada feature econométrica
  • Total de features: 117
  • Acurácia geral: 0.665

CONFIGURAÇÕES UTILIZADAS:
  • Período de dados: 15y
  • Horizonte de sinais: 1 dias
  • Lags OHLC: 10
  • Lags features econométricas: 5
  • Janela volatilidade: 20
  • Multiplicador spread: 0.5

FEATURES UTILIZADAS (117):
   1. Media_OHLC_PctChange_Lag_1
   2. Media_OHLC_PctChange_Lag_2
   3. Media_OHLC_PctChange_Lag_3
   4. Media_OHLC_PctChange_Lag_4
   5. Media_OHLC_PctChange_Lag_5
   6. Media_OHLC_PctChange_Lag_6
   7. Media_OHLC_PctChange_Lag_7
   8. Media_OHLC_PctChange_Lag_8
   9. Media_OHLC_PctChange_Lag_9
  10. Media_OHLC_PctChange_Lag_10
  11. Volume
  12. Spread
  13. Volatilidade
  14. Segunda
  15. Terca
  16. Quarta
  17. Quinta
  18. Sexta
  19. Mes_1
  20. Mes_2
  21. Mes_3
  22. Mes_4
  23. Mes_5
  24. Mes_6
  25. Mes_7
  26. Mes_8
  27. Mes_9
  28. Mes_10
  29. Mes_11
  30. Mes_12
  31. Quarter_1
  32. Quarter_2
  33. Quarter_3
  34. Quarter_4
  35. Last_Day_Quarter
  36. Pre_Feriado_Brasil
  37. Parkinson_Volatility
  38. MFI
  39. EMV
  40. EMV_MA
  41. Amihud
  42. Roll_Spread
  43. Hurst
  44. Vol_per_Volume
  45. CMF
  46. AD_Line
  47. VO
  48. Volume_Lag_1
  49. Volume_Lag_2
  50. Volume_Lag_3
  51. Volume_Lag_4
  52. Volume_Lag_5
  53. Spread_Lag_1
  54. Spread_Lag_2
  55. Spread_Lag_3
  56. Spread_Lag_4
  57. Spread_Lag_5
  58. Volatilidade_Lag_1
  59. Volatilidade_Lag_2
  60. Volatilidade_Lag_3
  61. Volatilidade_Lag_4
  62. Volatilidade_Lag_5
  63. Parkinson_Volatility_Lag_1
  64. Parkinson_Volatility_Lag_2
  65. Parkinson_Volatility_Lag_3
  66. Parkinson_Volatility_Lag_4
  67. Parkinson_Volatility_Lag_5
  68. MFI_Lag_1
  69. MFI_Lag_2
  70. MFI_Lag_3
  71. MFI_Lag_4
  72. MFI_Lag_5
  73. EMV_Lag_1
  74. EMV_Lag_2
  75. EMV_Lag_3
  76. EMV_Lag_4
  77. EMV_Lag_5
  78. EMV_MA_Lag_1
  79. EMV_MA_Lag_2
  80. EMV_MA_Lag_3
  81. EMV_MA_Lag_4
  82. EMV_MA_Lag_5
  83. Amihud_Lag_1
  84. Amihud_Lag_2
  85. Amihud_Lag_3
  86. Amihud_Lag_4
  87. Amihud_Lag_5
  88. Roll_Spread_Lag_1
  89. Roll_Spread_Lag_2
  90. Roll_Spread_Lag_3
  91. Roll_Spread_Lag_4
  92. Roll_Spread_Lag_5
  93. Hurst_Lag_1
  94. Hurst_Lag_2
  95. Hurst_Lag_3
  96. Hurst_Lag_4
  97. Hurst_Lag_5
  98. Vol_per_Volume_Lag_1
  99. Vol_per_Volume_Lag_2
  100. Vol_per_Volume_Lag_3
  101. Vol_per_Volume_Lag_4
  102. Vol_per_Volume_Lag_5
  103. CMF_Lag_1
  104. CMF_Lag_2
  105. CMF_Lag_3
  106. CMF_Lag_4
  107. CMF_Lag_5
  108. AD_Line_Lag_1
  109. AD_Line_Lag_2
  110. AD_Line_Lag_3
  111. AD_Line_Lag_4
  112. AD_Line_Lag_5
  113. VO_Lag_1
  114. VO_Lag_2
  115. VO_Lag_3
  116. VO_Lag_4
  117. VO_Lag_5

RESULTADOS DO MODELO:
  • Acurácia Geral: 0.665
  • Distribuição das Predições:
    - Venda: 8270 (48.6%)
    - Compra: 8759 (51.4%)

DEFINIÇÃO DOS SINAIS:
  • Sinal de Compra: Média OHLC atual < Média OHLC 1 dias à frente
  • Sinal de Venda: Média OHLC atual > Média OHLC 1 dias à frente
  • Sem Ação: Casos onde não há sinal de compra nem venda

PARÂMETROS DO XGBOOST:
  • n_estimators: 100
  • max_depth: 6
  • learning_rate: 0.1
  • random_state: 42
  • eval_metric: logloss
  • objective: multi:softprob (adicionado automaticamente)
  • num_class: 3 (adicionado automaticamente)
  • eval_metric: mlogloss (adicionado automaticamente)
